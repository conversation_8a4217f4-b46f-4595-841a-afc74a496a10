# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Deepseek API Configuration
VITE_DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Gemini API Configuration
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# Google OAuth Configuration (optional)
VITE_GOOGLE_CLIENT_ID=your_google_client_id_here

# Important: For Google OAuth to work properly, ensure your Google Cloud Console
# has the correct redirect URI configured:
# https://YOUR_SUPABASE_PROJECT_ID.supabase.co/auth/v1/callback
#
# Also ensure your Supabase project's Site URL is set to:
# http://localhost:3000 (for development)
# https://your-production-domain.com (for production)