import type { TranslationKeys } from '../lib/i18n';

export const en: TranslationKeys = {
  nav: {
    paraphrase: 'Paraphrase',
    summary: 'Summary',
    translation: 'Translation',
    plagiarism: 'Plagiarism Checker',
    contentDetector: 'AI Content Detector',
    chat: 'AI Chat',
    history: 'History',
    settings: 'Settings',
    upgrade: 'Upgrade',
    grammar: 'Grammar Check',
    transcription: 'Transcription',
    ocr: 'Image OCR',
  },

  common: {
    loading: 'Loading',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    copy: 'Copy',
    copied: 'Copied',
    reset: 'Reset',
    download: 'Download',
    export: 'Export',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    continue: 'Continue',
    confirm: 'Confirm',
    yes: 'Yes',
    no: 'No',
    characters: 'characters',
    words: 'words',
    operations: 'operations',
    unlimited: 'Unlimited',
    free: 'Free',
    premium: 'Premium',
    active: 'Active',
    inactive: 'Inactive',
    search: 'Search',
    refresh: 'Refresh',
    filters: 'Filters',
    status: 'Status',
    confidence: 'Confidence',
    processing: 'Processing',
    analyzing: 'Analyzing',
    connecting: 'Connecting',
    updating: 'Updating',
    saving: 'Saving',
  },

  auth: {
    signIn: 'Sign In',
    signUp: 'Sign Up',
    signOut: 'Sign Out',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    forgotPassword: 'Forgot Password',
    createAccount: 'Create Account',
    welcomeBack: 'Welcome Back',
    continueWithGoogle: 'Continue with Google',
    alreadyHaveAccount: 'Already have an account? Sign in',
    dontHaveAccount: "Don't have an account? Sign up",
    signInToContinue: 'Sign in to continue your writing journey',
    joinParaTextPro: 'Join ParaText Pro and unlock AI-powered writing',
    authenticating: 'Authenticating...',
    authenticationFailed: 'Authentication Failed',
    authenticationSuccess: 'Welcome!',
    authenticationProcessing: 'Processing your authentication...',
    authenticationComplete: 'Successfully authenticated! Redirecting...',
    authenticationError: 'Authentication session could not be established. Please try again.',
    returnToSignIn: 'Return to Sign In',
    googleSignInConfig: 'Google Sign-In Configuration',
    googleOAuthConfiguring: 'Google OAuth is being configured. Please use email/password authentication.',
    setupRequired: 'Setup Required',
    passwordStrength: 'Password Strength',
    passwordStrengthVeryWeak: 'Very Weak',
    passwordStrengthWeak: 'Weak',
    passwordStrengthFair: 'Fair',
    passwordStrengthGood: 'Good',
    passwordStrengthStrong: 'Strong',
    passwordsMatch: 'Passwords match',
    passwordsDoNotMatch: "Passwords don't match",
    dataEncrypted: 'Your data is encrypted and secure. We never store your passwords in plain text.',
  },

  paraphrase: {
    title: 'AI-Powered Paraphrasing',
    subtitle: 'Transform your text with advanced AI. Choose from multiple writing styles and get instant, high-quality paraphrasing with detailed improvements.',
    originalText: 'Original Text',
    paraphrasedText: 'Paraphrased Text',
    chooseWritingStyle: 'Choose Writing Style',
    paraphraseButton: 'Paraphrase',
    processing: 'Processing...',
    modes: {
      standard: 'Standard',
      formal: 'Formal',
      creative: 'Creative',
      shorten: 'Shorten',
      expand: 'Expand',
    },
    modeDescriptions: {
      standard: 'Natural rewriting while preserving meaning',
      formal: 'Professional tone for business contexts',
      creative: 'Engaging language with metaphors',
      shorten: 'Concise version preserving key points',
      expand: 'Detailed elaboration with examples',
    },
    improvements: 'Key Improvements:',
    readabilityScore: 'Score',
    placeholder: 'Enter your text here to get started with AI-powered paraphrasing...',
    emptyState: 'Your paraphrased text will appear here',
  },

  summary: {
    title: 'AI-Powered Summarization',
    subtitle: 'Transform lengthy content into comprehensive summaries that capture all key ideas and maintain the same level of detail and accuracy as the original text.',
    originalText: 'Original Text',
    summaryText: 'Summary',
    chooseSummaryStyle: 'Choose Summary Style',
    summarizeButton: 'Summarize',
    processing: 'Summarizing...',
    modes: {
      comprehensive: 'Comprehensive',
      executive: 'Executive',
      academic: 'Academic',
      bullet: 'Bullet Points',
      quick: 'Quick',
    },
    modeDescriptions: {
      comprehensive: 'Detailed summary maintaining all key ideas',
      executive: 'High-level overview for decision makers',
      academic: 'Scholarly summary with technical precision',
      bullet: 'Structured list of main points',
      quick: 'Brief overview of essential information',
    },
    keyPoints: 'Key Points Captured:',
    compressionRatio: 'shorter',
    placeholder: 'Enter your lengthy text here to get a comprehensive summary that maintains all key ideas and important details...',
    emptyState: 'Your comprehensive summary will appear here',
  },

  translation: {
    title: 'AI-Powered Translation',
    subtitle: 'Translate text between languages with high accuracy and natural fluency. Preserve context, tone, and meaning across linguistic boundaries.',
    sourceText: 'Source Text',
    translatedText: 'Translation',
    languageSelection: 'Language Selection',
    from: 'From',
    to: 'To',
    translateButton: 'Translate',
    processing: 'Translating...',
    autoDetect: 'Auto-detect',
    confidence: 'Confidence:',
    alternatives: 'Alternative Translations:',
    placeholder: 'Enter text to translate...',
    emptyState: 'Your translation will appear here',
    translating: 'Translating',
    swapLanguages: 'Swap languages',
    comparisonTitle: 'Translation Comparison',
    original: 'Original',
    translated: 'Translated',
    sourceWords: 'Source Words',
    translatedWords: 'Translated Words',
    wordDifference: 'Word Difference',
  },

  contentDetector: {
    title: 'AI Content Detector & Humanizer',
    subtitle: 'Powered by Advanced AI - Detect AI-generated content with sophisticated analysis and transform it into natural, human-like text while preserving the original meaning and context.',
    contentAnalysis: 'Content Analysis',
    humanizedVersion: 'Humanized Version',
    analyzing: 'Analyzing...',
    humanizing: 'Humanizing...',
    humanizeButton: 'Humanize',
    aiDetectionResults: 'AI Detection Results',
    humanizationSettings: 'Humanization Settings',
    creativityLevel: 'Creativity Level',
    preserveMeaning: 'Preserve exact meaning',
    aiProbability: 'AI Probability',
    confidence: 'Confidence',
    humanScore: 'Human Score',
    improvements: 'Humanization Improvements Applied',
    keyChanges: 'Key Changes Made',
    placeholder: 'Paste your text to analyze AI probability and humanize with advanced AI technology...',
    poweredBy: 'Powered by Advanced AI - Sophisticated Detection & Humanization',
    emptyState: 'AI Content Analysis & Humanization',
    creativityLow: 'Low - Minimal changes',
    creativityMedium: 'Medium - Balanced approach',
    creativityHigh: 'High - Extensive humanization',
    contentAnalysisHighlighting: 'Content Analysis with Highlighting',
    potentialPlagiarism: 'Potential plagiarism',
    originalContent: 'Original content',
    beforeAfterComparison: 'Before & After Comparison',
    originalAiScore: 'Original AI Score',
    humanizedScore: 'Humanized Score',
    improvement: 'Improvement',
    readyToHumanize: 'Ready to Humanize with Advanced AI',
    humanizeDescription: 'Click the "Humanize" button to transform your content into natural, human-like text while preserving the original meaning using advanced AI technology.',
    detectAI: 'Detect AI',
    humanWritten: 'Human Written',
    mixedContent: 'Mixed Content',
    aiGenerated: 'AI Generated',
    unknown: 'Unknown',
    aiGeneratedContent: 'AI-generated content',
    humanLikeContent: 'Human-like content',
    original: 'Original',
    humanized: 'Humanized',
    reason: 'Reason',
    human: 'human',
    humanLike: 'Human-like',
    emptyStateDescription: 'Enter your text above to detect AI-generated content and transform it into natural, human-like text with advanced AI technology.'
  },

  plagiarism: {
    title: 'AI Plagiarism Checker',
    subtitle: 'Detect potential plagiarism with advanced AI analysis. Get detailed source matching, similarity percentages, and actionable recommendations for improving content originality.',
    emptyState: 'Ready for Plagiarism Analysis',
    textAnalysis: 'Text Analysis',
    analyze: 'Analyze',
    analyzing: 'Analyzing...',
    plagiarismAnalysisResults: 'Plagiarism Analysis Results',
    sourcesFound: 'Sources Found',
    totalWords: 'Total Words',
    uniqueWords: 'Unique Words',
    originality: 'Originality',
    matchedSources: 'Matched Sources',
    writingStyle: 'Writing Style',
    patternRecognition: 'Pattern Recognition',
    vocabularyDiversity: 'Vocabulary Diversity',
    sentenceStructure: 'Sentence Structure',
    potentialMatch: 'Potential match with source',
    humanLike: 'Human-like',
    recommendationsImprovement: 'Recommendations for Improvement',
    placeholder: 'Paste your text here for analysis (minimum 100 words)...',
    match: 'match',
    matchedText: 'Matched text',
    contentAnalysisHighlighting: 'Content Analysis with Highlighting',
    emptyStateDescription: 'Enter at least 100 words to get a comprehensive plagiarism analysis with source matching and recommendations for improving originality.'
  },

  grammar: {
    title: 'AI Grammar & Style Checker',
    subtitle: 'Enhance your writing with advanced AI-powered grammar checking, spelling correction, and style improvements. Get real-time suggestions to make your text clear, correct, and compelling.',
    originalText: 'Original Text',
    correctedText: 'Corrected Text',
    checkGrammarButton: 'Check Grammar',
    processing: 'Analyzing...',
    placeholder: 'Enter your text here for comprehensive grammar and style analysis...',
    emptyState: 'Your grammar-checked text will appear here',
    corrections: 'Corrections',
    suggestions: 'Suggestions',
    acceptAll: 'Accept All',
    rejectAll: 'Reject All',
    accept: 'Accept',
    reject: 'Reject',
    noErrors: 'No errors found! Your text looks great.',
    errorsFound: 'errors found',
    grammarErrors: 'Grammar Errors',
    spellingErrors: 'Spelling Errors',
    styleImprovements: 'Style Improvements',
    applyChanges: 'Apply Changes',
    revertChanges: 'Revert to Original',
    grammarAnalysisResults: 'Grammar Analysis Results',
    grammarScore: 'Grammar Score',
    grammar: 'Grammar',
    spelling: 'Spelling',
    style: 'Style',
    original: 'Original',
    corrected: 'Corrected',
    improvementsMade: 'Improvements Made',
    grammarFixes: 'grammar fixes',
    spellingCorrections: 'spelling corrections',
    styleImprovements2: 'style improvements',
    accepted: 'Accepted',
    rejected: 'Rejected',
    passwordRequirements: 'Password Requirements:',
    atLeast8Characters: 'At least 8 characters long',
    containsLowercase: 'Contains lowercase letter',
    containsUppercase: 'Contains uppercase letter',
    containsNumber: 'Contains number',
  },

  transcription: {
    title: 'AI Document Transcription',
    subtitle: 'Upload PDF and document files for AI-powered text extraction and transcription, then enhance your content with powerful text processing tools.',
    uploadDocument: 'Upload Document for AI Transcription',
    dragDropText: 'Drag and drop your file here, or click to browse',
    browseFiles: 'Browse Files',
    supportedFormats: 'Supported formats',
    maxFileSize: 'Max size',
    processing: 'Processing document...',
    transcriptionComplete: 'Document transcribed successfully!',
    originalTranscription: 'AI-Extracted Text Content',
    processedResults: 'AI Processed Results',
    wordCount: 'words',
    readingTime: 'min read',
    fileName: 'File Name',
    fileSize: 'File Size',
    tools: {
      summarize: 'Summarize',
      paraphrase: 'Paraphrase',
      grammar: 'Grammar Check',
      plagiarism: 'AI Detection',
      translation: 'Translate',
      export: 'Export',
    },
    exportFormats: {
      txt: 'TXT',
      pdf: 'PDF',
      doc: 'DOC',
    },
    errors: {
      unsupportedFile: 'Unsupported file type. Please upload: PDF, DOC, DOCX, TXT',
      fileTooLarge: 'File size too large. Maximum size is 10MB.',
      uploadFailed: 'Failed to upload file. Please try again.',
      processingFailed: 'Failed to process document. Please try again.',
    },
    emptyState: 'Upload a document to get started with AI transcription',
    noResults: 'No AI processing results yet. Use the tools above to enhance your transcribed content with summarization, paraphrasing, grammar checking, and more.',
    dropDocument: 'Drop your document here',
    uploadForTranscription: 'Upload Document for AI Transcription',
    aiExtractedContent: 'AI-Extracted Text Content',
    aiProcessedResults: 'AI Processed Results',
    noProcessingResults: 'No AI Processing Results Yet',
    useToolsAbove: 'Use the AI processing tools above to enhance your transcribed content with summarization, paraphrasing, grammar checking, and more.',
    aiProcessingTools: 'AI Processing Tools',
    exportOptions: 'Export:',
    transcribedWithAi: 'Transcribed with AI',
    aiPoweredExtraction: 'AI-powered extraction',
    readyForTranscription: 'Ready for AI transcription',
    clickHumanize: 'Click Humanize',
    processedOn: 'Processed on',
  },

  ocr: {
    title: 'AI Image OCR',
    subtitle: 'Extract text from images with advanced OCR technology. Upload images and convert visual content to editable text with high accuracy.',
    uploadImage: 'Upload Image for OCR',
    dragDropText: 'Drag and drop your image here, or click to browse',
    browseFiles: 'Browse Files',
    supportedFormats: 'Supported formats',
    maxFileSize: 'Max size',
    processing: 'Processing image...',
    extractingText: 'Extracting text from image...',
    extractionComplete: 'Text extracted successfully!',
    extractedText: 'Extracted Text',
    processedResults: 'AI Processed Results',
    words: 'words',
    readingTime: 'min read',
    fileName: 'File Name',
    fileSize: 'File Size',
    tools: {
      summarize: 'Summarize',
      paraphrase: 'Paraphrase',
      grammar: 'Grammar Check',
      aiDetection: 'AI Detection',
      translation: 'Translate',
    },
    toolDescriptions: {
      summarize: 'Create comprehensive summary',
      paraphrase: 'Rewrite while preserving meaning',
      grammar: 'Fix grammar and style issues',
      aiDetection: 'Analyze content originality',
      translation: 'Translate to other languages',
    },
    errors: {
      unsupportedFile: 'Unsupported file type. Please upload: PNG, JPG, JPEG, BMP',
      fileTooLarge: 'File size too large. Maximum size is 10MB.',
      uploadFailed: 'Failed to upload image. Please try again.',
      processingFailed: 'Failed to process image. Please try again.',
      lowQualityImage: 'Low quality image. Text extraction may be incomplete.',
    },
    emptyState: 'Upload an image to extract text with OCR',
    noResults: 'No AI processing results yet. Use the tools above to enhance your extracted text with summarization, paraphrasing, grammar checking, and more.',
    dropImage: 'Drop your image here',
    readyForExtraction: 'Ready for OCR extraction',
    extractedWithAI: 'Extracted with AI OCR',
    aiPoweredExtraction: 'AI-powered OCR extraction',
    processingTools: 'Text Processing Tools',
    export: 'Export',
    sourceImage: 'Source Image',
    originalExtraction: 'Original extraction',
    noProcessingResults: 'No AI Processing Results Yet',
    useToolsAbove: 'Use the text processing tools above to enhance your extracted content',
    compression: 'Compression',
    keyPoints: 'Key Points',
    mode: 'Mode',
    readability: 'Readability',
    improvements: 'Improvements',
    score: 'Score',
    errorsFixed: 'Errors Fixed',
    aiProbability: 'AI Probability',
    confidence: 'Confidence',
    status: 'Status',
    from: 'From',
    to: 'To',
    processedOn: 'Processed on',
  },

  chat: {
    title: 'AI Chat Assistant',
    subtitle: 'Chat with your AI writing assistant for help with paraphrasing, summarizing, translation, and more',
    placeholder: 'Type your message here... (Press Enter to send, Shift+Enter for new line)',
    send: 'Send',
    typing: 'AI is typing...',
    you: 'You',
    assistant: 'AI Assistant',
    clearChat: 'Clear',
    exportChat: 'Export',
    messages: 'messages',
    welcomeMessage: "Hello! I'm your AI writing assistant. I can help you with paraphrasing, summarizing, translating, grammar checking, creative writing, and much more. How can I assist you today?",
    justNow: 'Just now',
    minutesAgo: 'm ago',
    hoursAgo: 'h ago',
    daysAgo: 'd ago',
    chatCleared: 'Chat cleared!',
    pressEnterToSend: 'Press Enter to send, Shift+Enter for new line',
  },

  history: {
    title: 'Activity History',
    subtitle: 'View and manage your past paraphrasing, summarization, and translation activities',
    searchPlaceholder: 'Search activities...',
    filters: 'Filters',
    allTypes: 'All Types',
    allStatus: 'All Status',
    sortBy: 'Sort By',
    date: 'Date',
    type: 'Type',
    status: 'Status',
    ascending: 'Ascending',
    descending: 'Descending',
    noActivities: 'No Activities Found',
    adjustFilters: 'Try adjusting your search or filter criteria',
    startUsing: 'Start using the app to see your activity history here',
    viewDetails: 'View Details',
    original: 'Original',
    result: 'Result',
    readability: 'Readability',
    compression: 'Compression',
    totalWords: 'Total Words',
    loadingHistory: 'Loading history...',
    activities: 'activities',
    refresh: 'Refresh',
    activityExported: 'Activity exported!',
    noActivitiesFound: 'No Activities Found',
    tryAdjustingFilters: 'Try adjusting your search or filter criteria',
    startUsingApp: 'Start using the app to see your activity history here',
  },

  settings: {
    title: 'Settings',
    subtitle: 'Manage your account, preferences, and subscription settings',
    profileInformation: 'Profile Information',
    subscription: 'Subscription',
    appearance: 'Appearance',
    additionalSettings: 'Additional Settings',
    firstName: 'First Name',
    lastName: 'Last Name',
    emailAddress: 'Email Address',
    editProfile: 'Edit Profile',
    saveChanges: 'Save Changes',
    saving: 'Saving...',
    currentPlan: 'Current Plan',
    upgradeToPro: 'Upgrade to Pro',
    manageBilling: 'Manage Billing',
    themePreference: 'Theme Preference',
    lightMode: 'Light Mode',
    darkMode: 'Dark Mode',
    chooseTheme: 'Choose between light and dark mode',
    privacySecurity: 'Privacy & Security',
    dataManagement: 'Data Management',
    changePassword: 'Change Password',
    twoFactorAuth: 'Two-Factor Authentication',
    exportAllData: 'Export All Data',
    clearAllData: 'Clear All Data',
    comingSoon: 'Coming Soon',
    permanent: 'Permanent',
    language: 'Language',
    selectLanguage: 'Select Language',
    languageFeatures: 'Language Features',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmNewPassword: 'Confirm New Password',
    updating: 'Updating...',
    setupRequired2: 'Setup Required:',
  },

  subscription: {
    freePlan: 'Free Plan',
    proPlan: 'Pro Plan',
    currentPlan: 'Current Plan',
    subscribeNow: 'Subscribe Now',
    upgradeNow: 'Upgrade Now',
    processing: 'Processing...',
    chooseYourPlan: 'Choose Your Plan',
    features: {
      unlimitedOperations: 'Unlimited operations',
      priorityProcessing: 'Priority processing',
      advancedModels: 'Advanced AI models',
      exportHistory: 'Export history',
      premiumSupport: 'Premium support',
    },
    billing: {
      monthly: 'month',
      yearly: 'year',
      nextBilling: 'Next Billing',
      renewsOn: 'Renews on',
      cancelAtPeriodEnd: 'Subscription will cancel at period end',
    },
    mostPopular: 'Most Popular',
    renewsOn: 'Renews on',
    activeSubscription: 'You have an active subscription',
    cancelCurrentPlan: 'Cancel your current plan to switch to this one',
    switchPlan: 'Switch Plan',
    securePayment: 'Secure payment powered by Stripe',
    paymentMethods: 'Visa • Mastercard • PayPal',
    currentSubscription: 'Current Subscription',
    billingPeriod: 'Billing Period',
    paymentMethod: 'Payment Method',
    planDetailsNotAvailable: 'Plan details not available',
    subscriptionWillCancel: 'Subscription will cancel at period end',
    whatsIncluded: 'What\'s Included',
    coreFeatures: 'Core Features',
    premiumBenefits: 'Premium Benefits',
    unlimitedParaphrasing: 'Unlimited paraphrasing with 5 modes',
    advancedSummarization: 'Advanced AI summarization',
    multiLanguageTranslation: 'Multi-language translation',
    aiContentDetection: 'AI content detection',
    textHumanization: 'Text humanization with advanced AI',
    grammarSpellChecking: 'Grammar and spell checking',
    priorityProcessingSpeed: 'Priority processing speed',
    completeActivityHistory: 'Complete activity history',
    exportDownloadCapabilities: 'Export and download capabilities',
    advancedAnalyticsInsights: 'Advanced analytics and insights',
    premiumCustomerSupport: 'Premium customer support',
    earlyAccessFeatures: 'Early access to new features',
  },

  usage: {
    dailyUsage: 'Daily Usage',
    dailyLimit: 'Daily limit reached',
    dailyLimitReached: "You've used all 20 operations for today. Upgrade to Premium for unlimited access.",
    operationsRemaining: 'operations remaining',
    upgradeForUnlimited: 'Consider upgrading to Premium for unlimited access.',
    resetsAtMidnight: 'Resets daily at midnight',
    premiumActive: 'Premium Active',
    minimumWords: 'Minimum 50 words recommended',
    recommendedWords: 'Minimum 100 words required',
    unlimitedOperations: 'Unlimited operations',
    operationsUsed: 'operations used',
    dailyLimitReachedTitle: 'Daily Limit Reached',
    reachedDailyLimit: 'You\'ve reached your daily limit of 20 operations. Upgrade to Premium for unlimited access to all AI-powered writing tools.',
    usageResetsAtMidnight: 'Your usage resets at midnight',
    premiumBenefitsTitle: 'Premium Benefits',
    unlimitedDailyOperations: 'Unlimited daily operations',
    priorityProcessingSpeed: 'Priority processing speed',
    advancedAiModels: 'Advanced AI models',
    exportCapabilities: 'Export capabilities',
    premiumSupport: 'Premium support',
    upgradeToPremiun: 'Upgrade to Premium - €5.00/month',
    continueWithFreePlan: 'Continue with Free Plan',
    freeOperationsReset: 'Your free operations will reset at midnight. No credit card required for free plan.',
  },

  messages: {
    error: {
      generic: 'An error occurred. Please try again.',
      network: 'Network error. Please check your connection.',
      authentication: 'Authentication failed',
      unauthorized: 'You are not authorized to perform this action',
      notFound: 'Resource not found',
      serverError: 'Server error. Please try again later.',
      validation: 'Please check your input and try again',
      required: 'This field is required',
      invalidEmail: 'Please enter a valid email address',
      passwordTooShort: 'Password must be at least 8 characters long',
      passwordsDoNotMatch: 'Passwords do not match',
      dailyLimitReached: 'Daily limit reached! Upgrade to Premium for unlimited access.',
      minimumWordsRequired: 'Please enter at least {count} words for accurate analysis',
    },
    success: {
      paraphrased: 'Text paraphrased successfully!',
      summarized: 'Text summarized successfully!',
      translated: 'Text translated successfully!',
      copied: 'Copied to clipboard!',
      saved: 'Saved successfully!',
      deleted: 'Deleted successfully!',
      exported: 'Exported successfully!',
      profileUpdated: 'Profile updated successfully!',
      passwordChanged: 'Password changed successfully!',
      subscriptionUpdated: 'Subscription updated successfully!',
      languageChanged: 'Language changed successfully!',
      grammarChecked: 'Grammar check completed successfully!',
      correctionsApplied: 'Corrections applied successfully!',
      transcriptionComplete: 'Document transcribed successfully with AI!',
      analyzed: 'Analysis completed successfully!',
      humanized: 'Text humanized successfully!',
      ocrComplete: 'Text extracted from image successfully!',
    },
  },

  languages: {
    english: 'English',
    arabic: 'العربية',
    french: 'Français',
    spanish: 'Español',
    autoDetect: 'Auto-detect',
  },
};